"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { StatusBar } from "react-native"
import { SafeAreaProvider } from 'react-native-safe-area-context'
import { AuthProvider } from './src/store/auth/AuthContext'
import { ProfileProvider } from './src/store/profile/ProfileContext'
import AppNavigator from "./src/navigation/AppNavigator"
import { initializeFirebase } from "./src/utils/firebase"
import { colors } from "./src/theme"
import AlertProvider from "./src/components/common/AlertProvider"

function App(): React.JSX.Element {
  const [initialized, setInitialized] = useState<boolean>(false)
  const [fcmToken, setFcmToken] = useState<string | null>(null)

  useEffect(() => {
    const setupFirebase = async () => {
      try {
        const token = await initializeFirebase()
        if (token) {
          setFcmToken(token)
          setInitialized(true)
        }
      } catch (error) {
        console.error("Error initializing Firebase:", error)
      }
    }

    setupFirebase()
  }, [])

  return (
    <SafeAreaProvider>
      <StatusBar backgroundColor={colors.primary} barStyle="light-content" />
      <AlertProvider>
        <AuthProvider>
          <ProfileProvider>
            <AppNavigator />
          </ProfileProvider>
        </AuthProvider>
      </AlertProvider>
    </SafeAreaProvider>
  )
}

export default App
