/**
 * Notification API Module
 * Handles all notification-related API calls for the mobile app
 */

import fetchClient from './fetchClient';
import logger from '../utils/logger';

const notificationApi = {
  /**
   * Get user notifications with pagination
   * @param {number} page - Page number (default: 1)
   * @param {number} limit - Number of items per page (default: 10)
   * @param {boolean} unreadOnly - Filter for unread notifications only
   * @returns {Promise} - Promise with notifications data
   */
  getNotifications: async (page = 1, limit = 10, unreadOnly = false) => {
    return logger.trackApiCall('notification', 'getNotifications', async () => {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(unreadOnly && { unreadOnly: 'true' })
      });

      const response = await fetchClient.get(`/notifications?${params}`);
      return response;
    });
  },

  /**
   * Get unread notification count
   * @returns {Promise} - Promise with unread count
   */
  getUnreadCount: async () => {
    return logger.trackApiCall('notification', 'getUnreadCount', async () => {
      const response = await fetchClient.get('/notifications/unread-count');
      return response;
    });
  },

  /**
   * Mark a notification as read
   * @param {string} notificationId - ID of the notification to mark as read
   * @returns {Promise} - Promise with success response
   */
  markAsRead: async (notificationId) => {
    return logger.trackApiCall('notification', 'markAsRead', async () => {
      const response = await fetchClient.patch(`/notifications/${notificationId}/read`);
      return response;
    });
  },

  /**
   * Mark all notifications as read
   * @returns {Promise} - Promise with success response
   */
  markAllAsRead: async () => {
    return logger.trackApiCall('notification', 'markAllAsRead', async () => {
      const response = await fetchClient.patch('/notifications/read-all');
      return response;
    });
  },

  /**
   * Delete a notification
   * @param {string} notificationId - ID of the notification to delete
   * @returns {Promise} - Promise with success response
   */
  deleteNotification: async (notificationId) => {
    return logger.trackApiCall('notification', 'deleteNotification', async () => {
      const response = await fetchClient.delete(`/notifications/${notificationId}`);
      return response;
    });
  },

  /**
   * Update FCM token for push notifications
   * @param {string} fcmToken - FCM token from Firebase
   * @returns {Promise} - Promise with success response
   */
  updateFCMToken: async (fcmToken) => {
    return logger.trackApiCall('notification', 'updateFCMToken', async () => {
      const response = await fetchClient.put('/notifications/fcm-token', {
        fcmToken
      });
      return response;
    });
  },
};

export default notificationApi;
