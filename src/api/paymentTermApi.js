import apiClient from './apiClient';
import logger from '../utils/logger';

/**
 * Payment Term API methods
 */
const paymentTermApi = {
  /**
   * Submit a payment term upgrade request
   * @param {Object} upgradeData - Payment term upgrade data
   * @param {string} upgradeData.requestedPaymentTerms - Requested payment term (IMMEDIATE, THIRTY_DAYS, SIXTY_DAYS)
   * @param {string} upgradeData.reason - Reason/message for the upgrade request
   * @returns {Promise} - Promise with upgrade request data
   */
  submitUpgradeRequest: async (upgradeData) => {
    return logger.trackApiCall('paymentTerm', 'submitUpgradeRequest', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.post('/payment-term-upgrades', upgradeData);
    });
  },

  /**
   * Get user's payment term upgrade requests history
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number for pagination
   * @param {number} params.limit - Number of items per page
   * @param {string} params.status - Filter by request status
   * @returns {Promise} - Promise with upgrade requests history data
   */
  getUpgradeRequests: async (params = {}) => {
    return logger.trackApiCall('paymentTerm', 'getUpgradeRequests', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.get('/payment-terms/upgrade-requests', { params });
    });
  },

  /**
   * Get current payment term details
   * @returns {Promise} - Promise with current payment term data
   */
  getCurrentPaymentTerm: async () => {
    return logger.trackApiCall('paymentTerm', 'getCurrentPaymentTerm', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.get('/payment-terms/current');
    });
  },

  /**
   * Get available payment term options
   * @returns {Promise} - Promise with available payment terms
   */
  getAvailablePaymentTerms: async () => {
    return logger.trackApiCall('paymentTerm', 'getAvailablePaymentTerms', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.get('/payment-terms/available');
    });
  },

  /**
   * Cancel a pending payment term upgrade request
   * @param {string} requestId - Request ID to cancel
   * @returns {Promise} - Promise with cancellation result
   */
  cancelUpgradeRequest: async (requestId) => {
    return logger.trackApiCall('paymentTerm', 'cancelUpgradeRequest', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.delete(`/payment-terms/upgrade-requests/${requestId}`);
    });
  },
};

export default paymentTermApi;
