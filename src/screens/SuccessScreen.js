"use client"

import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Button from '../components/common/Button';
import Icon from 'react-native-vector-icons/Feather';

/**
 * Success screen to show after order submission, registration, or payment term upgrade
 * @param {object} props
 * @param {object} props.route - Route params
 * @param {string} props.route.params.type - Type of success ('order', 'registration', or 'payment_term_upgrade')
 * @param {string} props.route.params.title - Title to display
 * @param {string} props.route.params.message - Message to display
 * @param {string} props.route.params.buttonText - Text for primary button
 * @param {string} props.route.params.buttonDestination - Destination screen for primary button
 * @param {object} props.navigation - Navigation object
 */
const SuccessScreen = ({ route, navigation }) => {
  // Extract type first to use in helper functions
  const { type = 'order' } = route.params || {};

  const getDefaultTitle = () => {
    switch (type) {
      case 'order':
        return 'Order Submitted for Approval';
      case 'registration':
        return 'Registration Submitted';
      case 'payment_term_upgrade':
        return 'Payment Term Upgrade Submitted';
      default:
        return 'Success';
    }
  };

  const getDefaultMessage = () => {
    switch (type) {
      case 'order':
        return 'Your order has been submitted for approval. We will notify you once it has been processed.';
      case 'registration':
        return 'Your registration has been submitted. We will notify you once your account has been approved.';
      case 'payment_term_upgrade':
        return 'Your payment term upgrade request has been submitted for approval. We will notify you once it has been processed.';
      default:
        return 'Your request has been submitted successfully.';
    }
  };

  const getDefaultButtonText = () => {
    switch (type) {
      case 'order':
        return 'View Orders';
      case 'registration':
        return 'Back to Login';
      case 'payment_term_upgrade':
        return 'Back to Profile';
      default:
        return 'Continue';
    }
  };

  const getDefaultButtonDestination = () => {
    switch (type) {
      case 'order':
        return 'OrdersTab';
      case 'registration':
        return 'Login';
      case 'payment_term_upgrade':
        return 'ProfileTab';
      default:
        return 'HomeTab';
    }
  };

  const {
    title = getDefaultTitle(),
    message = getDefaultMessage(),
    buttonText = getDefaultButtonText(),
    buttonDestination = getDefaultButtonDestination(),
  } = route.params || {};

  // Handle primary button press
  const handlePrimaryAction = () => {
    if (type === 'order' || type === 'payment_term_upgrade') {
      // For order or payment term upgrade success, navigate to specific tab in the main navigator
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'Tabs',
            state: {
              routes: [{ name: buttonDestination }]
            }
          }
        ],
      });
    } else {
      // For registration success, navigate to Login
      navigation.navigate(buttonDestination);
    }
  };

  // Handle secondary button press (go to home)
  const handleGoHome = () => {
    if (type === 'order' || type === 'payment_term_upgrade') {
      // Reset navigation to home tab
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'Tabs',
            state: {
              routes: [{ name: 'HomeTab' }]
            }
          }
        ],
      });
    }
  };

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Icon name="check-circle" size={80} color={colors.success} />
        </View>

        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>

        <View style={styles.buttonContainer}>
          <Button
            title={buttonText}
            onPress={handlePrimaryAction}
            style={styles.button}
          />

          {(type === 'order' || type === 'payment_term_upgrade') && (
            <Button
              title="Go to Home Page"
              variant="outline"
              onPress={handleGoHome}
              style={styles.button}
            />
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.large,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.lightGray || '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.large,
  },
  title: {
    ...textStyles.heading2,
    color: colors.textDark,
    textAlign: 'center',
    marginBottom: spacing.medium,
  },
  message: {
    ...textStyles.body1,
    color: colors.textLight,
    textAlign: 'center',
    marginBottom: spacing.xlarge,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 300,
  },
  button: {
    marginBottom: spacing.medium,
  },
});

export default SuccessScreen;
