import { useState } from 'react';
import { View, Text, StyleSheet, Modal, ScrollView, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { colors } from '../../theme/colors';
import { spacing } from '../../theme/spacing';
import { textStyles } from '../../theme/typography';
import Button from '../common/Button';
import { orderApi } from '../../api';
import errorHandler from '../../utils/errorHandler';

const OrderDetailsModal = ({ visible, order, onClose, onOrderUpdate }) => {
  const [isLoading, setIsLoading] = useState(false);

  // Function to format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const options = { day: 'numeric', month: 'long', year: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Function to get the status color
  const getStatusColor = (status) => {
    switch (status?.toUpperCase()) {
      case 'PENDING':
        return colors.warning;
      case 'APPROVED':
        return colors.primary;
      case 'SHIPPED':
        return colors.info;
      case 'DELIVERED':
        return colors.success;
      case 'CANCELLED':
        return colors.error;
      default:
        return colors.textLight;
    }
  };

  // Function to handle order cancellation
  const handleCancelOrder = async () => {
    if (!order?.id) {
      Alert.alert('Error', 'Unable to cancel order. Order ID is missing.');
      return;
    }

    Alert.alert(
      'Cancel Order',
      'Are you sure you want to cancel this order? This action cannot be undone.',
      [
        {
          text: 'No',
          style: 'cancel',
        },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            try {
              console.log('Cancelling order with ID:', order.id);
              await orderApi.cancelOrder(order.id);
              Alert.alert('Success', 'Order has been cancelled successfully');
              if (onOrderUpdate) {
                onOrderUpdate(); // Notify parent component about the update
              }
              onClose(); // Close the modal after successful cancellation
            } catch (error) {
              console.error('Error cancelling order:', error);
              errorHandler.handleApiError(error, {
                context: 'cancel_order',
                fallbackMessage: 'Failed to cancel order. Please try again.',
                showAlert: true,
              });
            } finally {
              setIsLoading(false);
            }
          },
        },
      ],
      { cancelable: true }
    );
  };

  if (!order) {
    return null;
  }

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Order Details</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.scrollContent}>
            <View style={styles.orderHeader}>
              <View>
                <Text style={styles.orderNumber}>Order #{order.orderNumber}</Text>
                <Text style={styles.date}>{formatDate(order.createdAt)}</Text>
              </View>

              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                <Text style={styles.statusText}>{order.status}</Text>
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Items</Text>
              {order.items && order.items.length > 0 ? order.items.map((item, index) => (
                <View key={item.id || index} style={styles.itemRow}>
                  <Text style={styles.quantity}>{item.quantity || 0}x</Text>
                  <Text style={styles.itemName}>
                    {`${item.stock?.type || item.type || 'N/A'} ${item.stock?.gsm || item.gsm || 'N/A'}gsm BF${item.stock?.bf || item.bf || 'N/A'}`}
                  </Text>
                  <Text style={styles.itemPrice}>₹{item.pricePerRoll || 0}</Text>
                  <Text style={styles.itemTotal}>₹{item.totalPrice || 0}</Text>
                </View>
              )) : (
                <Text style={styles.noItemsText}>No items found</Text>
              )}
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Fulfillment Details</Text>
              <View style={styles.fulfillmentRow}>
                <Text style={styles.fulfillmentLabel}>Type:</Text>
                <Text style={styles.fulfillmentValue}>
                  {order.fulfillmentType === 'SELF_PICKUP' ? '🚗 Self Pickup' : '🚚 Standard Shipping'}
                </Text>
              </View>

              {order.fulfillmentType === 'STANDARD_SHIPPING' && order.shippingAddress ? (
                <>
                  <Text style={styles.fulfillmentLabel}>Shipping Address:</Text>
                  <Text style={styles.addressText}>{order.shippingAddress.addressLine1}</Text>
                  {order.shippingAddress.addressLine2 && (
                    <Text style={styles.addressText}>{order.shippingAddress.addressLine2}</Text>
                  )}
                  <Text style={styles.addressText}>
                    {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}
                  </Text>
                </>
              ) : order.fulfillmentType === 'SELF_PICKUP' && order.vehicleNumber ? (
                <View style={styles.fulfillmentRow}>
                  <Text style={styles.fulfillmentLabel}>Vehicle Number:</Text>
                  <Text style={styles.fulfillmentValue}>{order.vehicleNumber}</Text>
                </View>
              ) : null}
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Payment Information</Text>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Payment Terms:</Text>
                <Text style={styles.paymentValue}>{order.paymentTerms || 'N/A'}</Text>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Payment Status:</Text>
                <Text style={[styles.paymentValue, { color: order.paymentStatus === 'PAID' ? colors.success : colors.warning }]}>
                  {order.paymentStatus || 'N/A'}
                </Text>
              </View>
              {order.paymentDueDate && (
                <View style={styles.paymentRow}>
                  <Text style={styles.paymentLabel}>Payment Due Date:</Text>
                  <Text style={styles.paymentValue}>{formatDate(order.paymentDueDate)}</Text>
                </View>
              )}
            </View>

            {order.notes && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Notes</Text>
                <Text style={styles.notesText}>{order.notes}</Text>
              </View>
            )}

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Order Timeline</Text>
              {order.createdAt && (
                <View style={styles.timelineItem}>
                  <Text style={styles.timelineDate}>{formatDate(order.createdAt)}</Text>
                  <Text style={styles.timelineEvent}>Order Placed</Text>
                </View>
              )}
              {order.approvedAt && (
                <View style={styles.timelineItem}>
                  <Text style={styles.timelineDate}>{formatDate(order.approvedAt)}</Text>
                  <Text style={styles.timelineEvent}>Order Approved</Text>
                </View>
              )}
              {order.shippedAt && (
                <View style={styles.timelineItem}>
                  <Text style={styles.timelineDate}>{formatDate(order.shippedAt)}</Text>
                  <Text style={styles.timelineEvent}>
                    {order.fulfillmentType === 'SELF_PICKUP' ? 'Ready for Pickup' : 'Order Shipped'}
                  </Text>
                </View>
              )}
              {order.deliveredAt && (
                <View style={styles.timelineItem}>
                  <Text style={styles.timelineDate}>{formatDate(order.deliveredAt)}</Text>
                  <Text style={styles.timelineEvent}>
                    {order.fulfillmentType === 'SELF_PICKUP' ? 'Order Collected' : 'Order Delivered'}
                  </Text>
                </View>
              )}
              {order.cancelledAt && (
                <View style={styles.timelineItem}>
                  <Text style={styles.timelineDate}>{formatDate(order.cancelledAt)}</Text>
                  <Text style={styles.timelineEvent}>Order Cancelled</Text>
                </View>
              )}
            </View>

            <View style={styles.pricingSection}>
              <View style={styles.pricingRow}>
                <Text style={styles.pricingLabel}>Subtotal:</Text>
                <Text style={styles.pricingValue}>₹{order.subtotal || order.totalAmount || 0}</Text>
              </View>

              {order.taxes && order.taxes > 0 && (
                <View style={styles.pricingRow}>
                  <Text style={styles.pricingLabel}>Taxes:</Text>
                  <Text style={styles.pricingValue}>₹{order.taxes}</Text>
                </View>
              )}

              {order.shipping && order.shipping > 0 && (
                <View style={styles.pricingRow}>
                  <Text style={styles.pricingLabel}>Shipping:</Text>
                  <Text style={styles.pricingValue}>₹{order.shipping}</Text>
                </View>
              )}

              <View style={styles.pricingRowTotal}>
                <Text style={styles.totalLabel}>Total Amount:</Text>
                <Text style={styles.totalValue}>₹{order.totalAmount || 0}</Text>
              </View>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            {isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={colors.primary} />
                <Text style={styles.loadingText}>Processing...</Text>
              </View>
            ) : (
              <>
                {order.status?.toUpperCase() === 'PENDING' && (
                  <Button
                    title="Cancel Order"
                    variant="outline"
                    onPress={handleCancelOrder}
                    style={styles.cancelButton}
                    disabled={isLoading}
                  />
                )}
                <Button
                  title="Close"
                  onPress={onClose}
                  style={order.status?.toUpperCase() === 'PENDING' ? styles.closeActionButton : styles.fullWidthButton}
                  disabled={isLoading}
                />
              </>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    padding: spacing.medium,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.medium,
    paddingBottom: spacing.small,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    ...textStyles.heading3,
    color: colors.textDark,
  },
  closeButton: {
    padding: spacing.small,
  },
  closeButtonText: {
    fontSize: 18,
    color: colors.textLight,
  },
  scrollContent: {
    flex: 1,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.medium,
  },
  orderNumber: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '600',
  },
  date: {
    ...textStyles.caption,
    color: colors.textLight,
  },
  statusBadge: {
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    borderRadius: 4,
  },
  statusText: {
    ...textStyles.caption,
    color: colors.white,
    fontWeight: '500',
  },
  section: {
    marginBottom: spacing.large,
  },
  sectionTitle: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '600',
    marginBottom: spacing.small,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.small,
  },
  quantity: {
    ...textStyles.body2,
    color: colors.textLight,
    width: 30,
  },
  itemName: {
    ...textStyles.body2,
    color: colors.textDark,
    flex: 1,
  },
  itemPrice: {
    ...textStyles.body2,
    color: colors.textLight,
    width: 80,
    textAlign: 'right',
  },
  itemTotal: {
    ...textStyles.body2,
    color: colors.textDark,
    fontWeight: '500',
    width: 80,
    textAlign: 'right',
  },
  addressText: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.tiny,
  },
  fulfillmentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.small,
  },
  fulfillmentLabel: {
    ...textStyles.body2,
    color: colors.textLight,
    fontWeight: '500',
  },
  fulfillmentValue: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: '500',
  },
  notesText: {
    ...textStyles.body2,
    color: colors.textDark,
  },
  noItemsText: {
    ...textStyles.body2,
    color: colors.textLight,
    fontStyle: 'italic',
    textAlign: 'center',
    marginVertical: spacing.small,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.small,
  },
  paymentLabel: {
    ...textStyles.body2,
    color: colors.textLight,
  },
  paymentValue: {
    ...textStyles.body2,
    color: colors.textDark,
    fontWeight: '500',
  },
  timelineItem: {
    marginBottom: spacing.small,
  },
  timelineDate: {
    ...textStyles.caption,
    color: colors.textLight,
  },
  timelineEvent: {
    ...textStyles.body2,
    color: colors.textDark,
  },
  pricingSection: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.medium,
    marginBottom: spacing.medium,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.small,
  },
  pricingLabel: {
    ...textStyles.body2,
    color: colors.textLight,
  },
  pricingValue: {
    ...textStyles.body2,
    color: colors.textDark,
  },
  pricingRowTotal: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.small,
    paddingTop: spacing.small,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  totalLabel: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '600',
  },
  totalValue: {
    ...textStyles.body1,
    color: colors.primary,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.medium,
  },
  cancelButton: {
    flex: 1,
    marginRight: spacing.small,
  },
  closeActionButton: {
    flex: 1,
    marginLeft: spacing.small,
  },
  fullWidthButton: {
    flex: 1,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  loadingText: {
    ...textStyles.body2,
    color: colors.textLight,
    marginLeft: spacing.small,
  },
});

export default OrderDetailsModal;
