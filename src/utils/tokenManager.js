import AsyncStorage from '@react-native-async-storage/async-storage';
import { getAuth } from '@react-native-firebase/auth';
import alertManager from './alertManager';
import logger from './simpleLogger';

class TokenManager {
  constructor() {
    this.isRefreshing = false;
    this.refreshPromise = null;
    this.expirationCheckInterval = null;
    this.onTokenExpiredCallbacks = [];
  }

  /**
   * Initialize token management
   */
  initialize() {
    // Check token expiration every 5 minutes
    this.expirationCheckInterval = setInterval(() => {
      this.checkTokenExpiration();
    }, 5 * 60 * 1000);

    // Initial check
    this.checkTokenExpiration();
  }

  /**
   * Clean up token management
   */
  cleanup() {
    if (this.expirationCheckInterval) {
      clearInterval(this.expirationCheckInterval);
      this.expirationCheckInterval = null;
    }
    this.onTokenExpiredCallbacks = [];
  }

  /**
   * Add callback for token expiration events
   * @param {Function} callback - Function to call when token expires
   * @returns {Function} - Unsubscribe function
   */
  onTokenExpired(callback) {
    this.onTokenExpiredCallbacks.push(callback);
    
    return () => {
      const index = this.onTokenExpiredCallbacks.indexOf(callback);
      if (index > -1) {
        this.onTokenExpiredCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Check if current token is expired or about to expire
   */
  async checkTokenExpiration() {
    try {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        logger.log('No authenticated user found');
        return;
      }

      // Get token result with claims
      const tokenResult = await user.getIdTokenResult();
      const expirationTime = new Date(tokenResult.expirationTime);
      const currentTime = new Date();
      
      // Check if token expires within the next 5 minutes
      const timeUntilExpiration = expirationTime.getTime() - currentTime.getTime();
      const fiveMinutes = 5 * 60 * 1000;

      if (timeUntilExpiration <= 0) {
        // Token has expired
        logger.log('Token has expired');
        this.handleTokenExpiration();
      } else if (timeUntilExpiration <= fiveMinutes) {
        // Token expires soon, refresh it
        logger.log('Token expires soon, refreshing...');
        await this.refreshToken();
      }
    } catch (error) {
      logger.error('Error checking token expiration:', error);
      // If we can't check the token, it might be invalid
      this.handleTokenExpiration();
    }
  }

  /**
   * Refresh the current Firebase token
   * @param {boolean} forceRefresh - Force refresh even if token is still valid
   * @returns {Promise<string|null>} - New token or null if failed
   */
  async refreshToken(forceRefresh = false) {
    // Prevent multiple simultaneous refresh attempts
    if (this.isRefreshing) {
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this._performTokenRefresh(forceRefresh);

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Internal method to perform token refresh
   * @param {boolean} forceRefresh - Force refresh even if token is still valid
   * @returns {Promise<string|null>} - New token or null if failed
   */
  async _performTokenRefresh(forceRefresh) {
    try {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        logger.log('No authenticated user for token refresh');
        return null;
      }

      // Get fresh token
      const newToken = await user.getIdToken(forceRefresh);
      
      if (newToken) {
        // Store the new token
        await AsyncStorage.setItem('auth_token', newToken);
        logger.log('Token refreshed successfully');
        return newToken;
      } else {
        logger.error('Failed to get new token');
        return null;
      }
    } catch (error) {
      logger.error('Error refreshing token:', error);
      
      // If refresh fails, the token might be completely invalid
      if (error.code === 'auth/user-token-expired' || 
          error.code === 'auth/invalid-user-token') {
        this.handleTokenExpiration();
      }
      
      return null;
    }
  }

  /**
   * Handle token expiration by notifying callbacks and showing user feedback
   */
  handleTokenExpiration() {
    logger.log('Handling token expiration');

    // Notify all registered callbacks
    this.onTokenExpiredCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        logger.error('Error in token expiration callback:', error);
      }
    });

    // Show user-friendly message
    alertManager.showWarning(
      'Session Expired',
      'Your session has expired. Please log in again to continue.',
      [
        {
          text: 'Log In',
          onPress: () => {
            // The callback should handle navigation to login
            this.onTokenExpiredCallbacks.forEach(callback => callback());
          }
        }
      ]
    );
  }

  /**
   * Get current token, refreshing if necessary
   * @returns {Promise<string|null>} - Current valid token or null
   */
  async getCurrentToken() {
    try {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        return null;
      }

      // Get token, this will automatically refresh if needed
      const token = await user.getIdToken();
      
      // Update stored token
      if (token) {
        await AsyncStorage.setItem('auth_token', token);
      }
      
      return token;
    } catch (error) {
      logger.error('Error getting current token:', error);
      
      if (error.code === 'auth/user-token-expired' || 
          error.code === 'auth/invalid-user-token') {
        this.handleTokenExpiration();
      }
      
      return null;
    }
  }

  /**
   * Check if a token is valid (not expired)
   * @param {string} token - Token to validate
   * @returns {Promise<boolean>} - True if valid, false otherwise
   */
  async isTokenValid(token) {
    if (!token) return false;

    try {
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) return false;

      // Try to get token result
      const tokenResult = await user.getIdTokenResult();
      const expirationTime = new Date(tokenResult.expirationTime);
      const currentTime = new Date();

      return expirationTime.getTime() > currentTime.getTime();
    } catch (error) {
      logger.error('Error validating token:', error);
      return false;
    }
  }

  /**
   * Clear stored token
   */
  async clearToken() {
    try {
      await AsyncStorage.removeItem('auth_token');
      logger.log('Token cleared');
    } catch (error) {
      logger.error('Error clearing token:', error);
    }
  }
}

// Create and export singleton instance
const tokenManager = new TokenManager();

export default tokenManager;
