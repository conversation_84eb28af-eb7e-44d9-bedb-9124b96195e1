// Fixed errorHandler.js to use alertManager consistently
import logger from './simpleLogger';
import alertManager from './alertManager';

/**
 * Centralized error handling utility
 * @type {Object}
 */
const errorHandler = {
  /**
   * Handle API errors consistently across the app
   *
   * @param {Error} error - The error object
   * @param {Object} options - Error handling options
   * @param {string} options.context - Context where the error occurred (e.g., 'login', 'registration')
   * @param {string} options.fallbackMessage - Default message to show if error doesn't have a message
   * @param {boolean} options.showAlert - Whether to show an alert dialog
   * @param {Function} options.onError - Optional callback to execute after error is handled
   */
  handleApiError: (error, {
    context = 'api_call',
    fallbackMessage = 'Something went wrong. Please try again.',
    showAlert = true,
    onError = null
  } = {}) => {
    // Log the error for debugging
    logger.error(`API Error in ${context}`, {
      error: error,
      message: error?.message,
      status: error?.status,
      data: error?.data
    });

    // Get readable message
    let errorMessage = fallbackMessage;

    try {
      // Safely extract message from various error formats
      // Handle the case where error might be an object instead of Error instance
      if (typeof error === 'object' && error !== null) {
        // Check for validation errors array
        if (error.data?.errors && Array.isArray(error.data.errors)) {
          const validationErrors = error.data.errors;
          errorMessage = validationErrors.map(err => {
            if (typeof err === 'object' && err.field && err.message) {
              return `${err.field}: ${err.message}`;
            }
            return String(err);
          }).join('\n');
        }
        // Check for direct message in data
        else if (error.data?.message && typeof error.data.message === 'string') {
          errorMessage = error.data.message;
        }
        // Check for error field in data
        else if (error.data?.error && typeof error.data.error === 'string') {
          errorMessage = error.data.error;
        }
        // Check for message property on error object
        else if (error.message && typeof error.message === 'string' && !error.message.includes('Network Error')) {
          errorMessage = error.message;
        }
        // Handle case where error object is passed directly as message
        else if (typeof error === 'object' && !error.message && !error.data) {
          // This handles the "error is not a function it is an object" case
          errorMessage = 'An error occurred. Please try again.';
          logger.warn('Error object passed without proper message structure:', error);
        }
      }
      // Handle string errors
      else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Special handling for database errors
      if (error.status === 500 && errorMessage.includes('table')) {
        errorMessage = 'Database error: Tables not initialized. Please contact support.';
      }

      // Ensure errorMessage is always a string
      if (typeof errorMessage !== 'string') {
        errorMessage = fallbackMessage;
        logger.warn('Error message was not a string, using fallback:', { errorMessage, fallbackMessage });
      }

    } catch (messageExtractionError) {
      // If there's an error extracting the message, use fallback
      logger.error('Error extracting error message:', messageExtractionError);
      errorMessage = fallbackMessage;
    }

    // Show alert if needed
    if (showAlert) {
      try {
        // Use custom alert manager if available, otherwise fall back to native Alert
        alertManager.showError('Error', errorMessage, [{
          text: 'OK',
          onPress: () => {
            console.log('User acknowledged error in', context);
            // Call the callback if provided
            if (typeof onError === 'function') {
              try {
                onError(errorMessage, error);
              } catch (callbackError) {
                logger.error('Error in onError callback:', callbackError);
              }
            }
          }
        }]);
      } catch (alertError) {
        // Fallback to console if alert fails
        logger.error('Failed to show error alert:', alertError);
        console.error('Error in', context, ':', errorMessage);
      }
    } else if (typeof onError === 'function') {
      // If no alert but we have a callback, call it safely
      try {
        onError(errorMessage, error);
      } catch (callbackError) {
        logger.error('Error in onError callback:', callbackError);
      }
    }

    return errorMessage;
  },

  /**
   * Handle form validation errors
   *
   * @param {Object} validationResult - Result from form validation
   * @param {boolean} showAlert - Whether to show an alert
   * @returns {boolean} - Returns true if form is valid
   */
  handleValidationError: (validationResult, showAlert = true) => {
    if (validationResult.isValid) {
      return true;
    }

    if (showAlert) {
      alertManager.showWarning('Validation Error', validationResult.message, [{
        text: 'OK',
        onPress: () => {
          console.log('User acknowledged validation error');
        }
      }]);
    }

    return false;
  }
};

export default errorHandler;
