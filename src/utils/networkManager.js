import NetInfo from '@react-native-community/netinfo';
import alertManager from './alertManager';

class NetworkManager {
  constructor() {
    this.isConnected = true;
    this.listeners = [];
    this.unsubscribe = null;
    this.hasShownOfflineAlert = false;
  }

  /**
   * Initialize network monitoring
   */
  initialize() {
    // Subscribe to network state changes
    this.unsubscribe = NetInfo.addEventListener(state => {
      const wasConnected = this.isConnected;
      this.isConnected = state.isConnected && state.isInternetReachable;
      
      // Notify listeners of network state change
      this.listeners.forEach(listener => {
        listener(this.isConnected, wasConnected);
      });

      // Show alerts for connectivity changes
      if (wasConnected && !this.isConnected) {
        this.showOfflineAlert();
      } else if (!wasConnected && this.isConnected) {
        this.showOnlineAlert();
      }
    });

    // Get initial network state
    NetInfo.fetch().then(state => {
      this.isConnected = state.isConnected && state.isInternetReachable;
      
      // Show offline alert if initially offline
      if (!this.isConnected) {
        this.showOfflineAlert();
      }
    });
  }

  /**
   * Clean up network monitoring
   */
  cleanup() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
    this.listeners = [];
  }

  /**
   * Add a listener for network state changes
   * @param {Function} listener - Callback function (isConnected, wasConnected) => void
   * @returns {Function} - Unsubscribe function
   */
  addListener(listener) {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Get current network status
   * @returns {boolean} - True if connected, false otherwise
   */
  getConnectionStatus() {
    return this.isConnected;
  }

  /**
   * Check if device is currently online
   * @returns {Promise<boolean>} - Promise resolving to connection status
   */
  async checkConnection() {
    try {
      const state = await NetInfo.fetch();
      this.isConnected = state.isConnected && state.isInternetReachable;
      return this.isConnected;
    } catch (error) {
      console.error('Error checking network connection:', error);
      return false;
    }
  }

  /**
   * Show offline alert
   */
  showOfflineAlert() {
    if (!this.hasShownOfflineAlert) {
      this.hasShownOfflineAlert = true;
      alertManager.showWarning(
        'No Internet Connection',
        'You are currently offline. Some features may not work properly until you reconnect to the internet.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Reset flag after a delay to allow showing the alert again if needed
              setTimeout(() => {
                this.hasShownOfflineAlert = false;
              }, 30000); // 30 seconds
            }
          }
        ]
      );
    }
  }

  /**
   * Show online alert
   */
  showOnlineAlert() {
    if (this.hasShownOfflineAlert) {
      this.hasShownOfflineAlert = false;
      alertManager.showSuccess(
        'Connection Restored',
        'You are back online! All features are now available.',
        [{ text: 'OK' }]
      );
    }
  }

  /**
   * Execute a function only if online, show alert if offline
   * @param {Function} onlineCallback - Function to execute when online
   * @param {string} offlineMessage - Custom message to show when offline
   */
  executeIfOnline(onlineCallback, offlineMessage = 'This action requires an internet connection.') {
    if (this.isConnected) {
      onlineCallback();
    } else {
      alertManager.showWarning(
        'No Internet Connection',
        offlineMessage,
        [{ text: 'OK' }]
      );
    }
  }

  /**
   * Wrap an async function to handle network errors gracefully
   * @param {Function} asyncFunction - Async function to wrap
   * @param {Object} options - Options for error handling
   * @returns {Function} - Wrapped function
   */
  wrapAsyncFunction(asyncFunction, options = {}) {
    const {
      showNetworkError = true,
      fallbackMessage = 'Please check your internet connection and try again.'
    } = options;

    return async (...args) => {
      try {
        // Check connection before executing
        if (!this.isConnected) {
          if (showNetworkError) {
            alertManager.showWarning(
              'No Internet Connection',
              fallbackMessage,
              [{ text: 'OK' }]
            );
          }
          throw new Error('No internet connection');
        }

        return await asyncFunction(...args);
      } catch (error) {
        // Check if error is network-related
        if (this.isNetworkError(error)) {
          if (showNetworkError) {
            alertManager.showError(
              'Network Error',
              fallbackMessage,
              [{ text: 'OK' }]
            );
          }
        }
        throw error;
      }
    };
  }

  /**
   * Check if an error is network-related
   * @param {Error} error - Error to check
   * @returns {boolean} - True if network error, false otherwise
   */
  isNetworkError(error) {
    if (!error) return false;
    
    const networkErrorMessages = [
      'network error',
      'network request failed',
      'no internet connection',
      'connection timeout',
      'request timeout',
      'unable to connect',
      'connection refused',
      'dns lookup failed'
    ];

    const errorMessage = error.message?.toLowerCase() || '';
    return networkErrorMessages.some(msg => errorMessage.includes(msg));
  }
}

// Create and export singleton instance
const networkManager = new NetworkManager();

export default networkManager;
